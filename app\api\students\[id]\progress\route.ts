import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if user has permission to view this student's progress
    if (!session.user.role || !['ADMIN', 'MANAGER', 'RECEPTION'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get student information
    const student = await prisma.student.findUnique({
      where: { userId: id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        currentGroup: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
        attendances: {
          include: {
            class: {
              select: {
                date: true,
                topic: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: 50,
        },
        assessments: {
          orderBy: { createdAt: 'desc' },
          take: 20,
        },
        payments: {
          where: { status: 'PAID' },
          orderBy: { paidDate: 'desc' },
        },
      },
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Calculate progress metrics
    const totalClasses = student.attendances.length
    const attendedClasses = student.attendances.filter(a => a.status === 'PRESENT').length
    const attendanceRate = totalClasses > 0 ? (attendedClasses / totalClasses) * 100 : 0

    // Calculate assessment average
    const completedAssessments = student.assessments.filter(a => a.score !== null)
    const averageScore = completedAssessments.length > 0 
      ? completedAssessments.reduce((sum, a) => sum + (a.score || 0), 0) / completedAssessments.length
      : 0

    // Calculate level progress (mock calculation based on assessments and attendance)
    const levelProgress = Math.min(100, (attendanceRate * 0.4) + (averageScore * 0.6))

    // Get next level
    const levelOrder = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']
    const currentLevelIndex = levelOrder.indexOf(student.level)
    const nextLevel = currentLevelIndex >= 0 && currentLevelIndex < levelOrder.length - 1 
      ? levelOrder[currentLevelIndex + 1] 
      : null

    // Calculate skill breakdown based on assessment results
    const skillAssessments = await prisma.assessment.findMany({
      where: {
        studentId: student.id,
        completedAt: { not: null }
      },
      orderBy: { completedAt: 'desc' },
      take: 10 // Last 10 assessments
    })

    // Calculate skill progress based on assessment results
    const skillProgress = calculateSkillProgress(skillAssessments, averageScore, student.level)
    const skills = [
      { name: "Speaking", progress: skillProgress.speaking, level: student.level },
      { name: "Listening", progress: skillProgress.listening, level: student.level },
      { name: "Reading", progress: skillProgress.reading, level: student.level },
      { name: "Writing", progress: skillProgress.writing, level: student.level },
      { name: "Grammar", progress: skillProgress.grammar, level: student.level },
      { name: "Vocabulary", progress: skillProgress.vocabulary, level: student.level },
    ]

    // Get recent achievements (based on assessments and attendance)
    const achievements = []
    if (attendanceRate >= 95) {
      achievements.push({ name: "Perfect Attendance", date: new Date().toISOString(), icon: "🎯" })
    }
    if (averageScore >= 90) {
      achievements.push({ name: "Excellence Award", date: new Date().toISOString(), icon: "🏆" })
    }
    if (completedAssessments.length >= 10) {
      achievements.push({ name: "Assessment Master", date: new Date().toISOString(), icon: "📚" })
    }

    const progressData = {
      student: {
        name: student.user.name,
        email: student.user.email,
        level: student.level,
        nextLevel,
        branch: student.branch,
      },
      currentGroup: student.currentGroup ? {
        name: student.currentGroup.name,
        course: student.currentGroup.course.name,
        level: student.currentGroup.course.level,
      } : null,
      progress: {
        overall: Math.round(levelProgress),
        attendance: Math.round(attendanceRate),
        averageScore: Math.round(averageScore),
      },
      statistics: {
        totalClasses,
        attendedClasses,
        completedAssessments: completedAssessments.length,
        pendingAssessments: student.assessments.filter(a => a.score === null).length,
        totalPayments: student.payments.length,
        totalPaid: student.payments.reduce((sum, p) => sum + Number(p.amount), 0),
      },
      skills,
      achievements,
      recentActivity: {
        assessments: student.assessments.slice(0, 5).map(a => ({
          id: a.id,
          testName: a.testName,
          score: a.score,
          maxScore: a.maxScore,
          passed: a.passed,
          completedAt: a.completedAt,
        })),
        attendance: student.attendances.slice(0, 10).map(a => ({
          id: a.id,
          date: a.class.date,
          topic: a.class.topic,
          status: a.status,
        })),
      },
    }

    return NextResponse.json(progressData)
  } catch (error) {
    console.error('Error fetching student progress:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function calculateSkillProgress(assessments: any[], averageScore: number, studentLevel: string) {
  // If no assessments, use average score with skill-specific adjustments
  if (assessments.length === 0) {
    const baseScore = Math.max(0, Math.min(100, averageScore))
    // Use skill-specific multipliers based on typical learning patterns
    return {
      speaking: Math.max(0, Math.min(100, baseScore * 0.9)), // Speaking tends to be slightly lower
      listening: Math.max(0, Math.min(100, baseScore * 1.1)), // Listening tends to be higher
      reading: Math.max(0, Math.min(100, baseScore * 1.05)), // Reading slightly above average
      writing: Math.max(0, Math.min(100, baseScore * 0.85)), // Writing tends to be lower
      grammar: Math.max(0, Math.min(100, baseScore)), // Grammar at average
      vocabulary: Math.max(0, Math.min(100, baseScore * 0.95)), // Vocabulary slightly below
    }
  }

  // Extract skill scores from assessment results
  const skillScores = {
    speaking: [] as number[],
    listening: [] as number[],
    reading: [] as number[],
    writing: [] as number[],
    grammar: [] as number[],
    vocabulary: [] as number[]
  }

  assessments.forEach(assessment => {
    if (assessment.results && typeof assessment.results === 'object') {
      const results = assessment.results as any

      // Try to extract skill-specific scores from results
      Object.keys(skillScores).forEach(skill => {
        if (results[skill] !== undefined) {
          skillScores[skill as keyof typeof skillScores].push(Number(results[skill]))
        } else if (results.skills && results.skills[skill] !== undefined) {
          skillScores[skill as keyof typeof skillScores].push(Number(results.skills[skill]))
        }
      })
    }
  })

  // Calculate average for each skill, fallback to overall average with variation
  const calculateSkillAverage = (scores: number[], skillName: string) => {
    if (scores.length > 0) {
      return scores.reduce((sum, score) => sum + score, 0) / scores.length
    }
    // Fallback with skill-specific tendencies
    const skillMultipliers = {
      speaking: 0.9,   // Speaking tends to be slightly lower
      listening: 1.1,  // Listening tends to be slightly higher
      reading: 1.05,   // Reading tends to be slightly higher
      writing: 0.85,   // Writing tends to be lower
      grammar: 1.0,    // Grammar is average
      vocabulary: 0.95 // Vocabulary is slightly below average
    }
    const multiplier = skillMultipliers[skillName as keyof typeof skillMultipliers] || 1.0
    return Math.max(0, Math.min(100, averageScore * multiplier))
  }

  return {
    speaking: calculateSkillAverage(skillScores.speaking, 'speaking'),
    listening: calculateSkillAverage(skillScores.listening, 'listening'),
    reading: calculateSkillAverage(skillScores.reading, 'reading'),
    writing: calculateSkillAverage(skillScores.writing, 'writing'),
    grammar: calculateSkillAverage(skillScores.grammar, 'grammar'),
    vocabulary: calculateSkillAverage(skillScores.vocabulary, 'vocabulary'),
  }
}
